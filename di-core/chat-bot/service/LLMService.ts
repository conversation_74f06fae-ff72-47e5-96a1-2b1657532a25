import { Inject } from 'typescript-ioc';
import { LLMRepository } from '../repository/LLMRepository';

export abstract class LLMService {
  abstract generateChart(messages: string): Promise<string>;
}

export class LLMServiceImpl implements LLMService {
  @Inject
  private llmRepository!: LLMRepository;

  generateChart(messages: string): Promise<string> {
    return this.llmRepository.generateChart(messages);
  }
}

export class MockLLMService extends LLMService {
  generateChart(messages: string): Promise<string> {
    return Promise.resolve(
      `
I'll create a pie chart showing total profit by region for the year 2025. Based on your request, I'll use the Total_Profit field summed by region and filter for orders in 2025.

\`\`\`
{
"chart_type": "kpi",
  "query_setting": {
  "filters": [
    {
      "class_name": "and",
      "conditions": [
        {
          "field": {
            "class_name": "table_field",
            "db_name": "sample",
            "tbl_name": "sale",
            "field_name": "Item_Type",
            "field_type": "string"
          },
          "class_name": "in",
          "possible_values": [
            "Baby Food",
            "Beverages",
            "Cereal"
          ]
        },
        {
          "field": {
            "class_name": "table_field",
            "db_name": "sample",
            "tbl_name": "sale",
            "field_name": "Ship_Date",
            "field_type": "datetime"
          },
          "class_name": "between",
          "min": "2025-08-01 00:00:00",
          "max": "2025-08-31 23:59:59"
        }
      ]
    }
  ],
  "sorts": [],
  "options": {},
  "sql_views": [],
  "parameters": {},
  "value": {
    "name": "Sales Channel",
    "function": {
      "field": {
        "class_name": "table_field",
        "db_name": "sample",
        "tbl_name": "sale",
        "field_name": "Sales_Channel",
        "field_type": "string"
      },
      "class_name": "count"
    },
    "is_horizontal_view": false,
    "is_collapse": false,
    "is_calc_group_total": true,
    "is_calc_min_max": false,
    "is_dynamic_function": false
  },
  "class_name": "number_chart_setting"
}
}
\`\`\`
      `
    );
  }
}
